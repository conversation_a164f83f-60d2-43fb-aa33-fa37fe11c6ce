<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - Socket Relay</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .system-info {
            background: rgba(102, 126, 234, 0.1);
            border-left: 4px solid #667eea;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="card login-card">
                    <div class="card-header login-header text-center py-4">
                        <h3 class="mb-0">
                            <i class="bi bi-shield-lock"></i>
                            Socket Relay
                        </h3>
                        <p class="mb-0 mt-2 opacity-75">TCP/UDP转发与管理系统</p>
                    </div>
                    <div class="card-body p-4">
                        <form id="loginForm">
                            <div class="mb-3">
                                <label for="username" class="form-label">
                                    <i class="bi bi-person"></i> 用户名
                                </label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="admin" required>
                            </div>
                            <div class="mb-4">
                                <label for="password" class="form-label">
                                    <i class="bi bi-lock"></i> 密码
                                </label>
                                <input type="password" class="form-control" id="password" name="password" 
                                       value="admin123" required>
                            </div>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-login">
                                    <i class="bi bi-box-arrow-in-right"></i> 登录
                                </button>
                            </div>
                        </form>
                        
                        <div class="system-info mt-4 p-3">
                            <h6 class="mb-2">
                                <i class="bi bi-info-circle"></i> 系统信息
                            </h6>
                            <small class="text-muted">
                                <div>版本: v1.0</div>
                                <div>作者: 小白很菜</div>
                                <div>日期: 2025-08-02</div>
                            </small>
                        </div>
                    </div>
                </div>
                
                <!-- 功能特性 -->
                <div class="card mt-4" style="background: rgba(255, 255, 255, 0.9);">
                    <div class="card-body">
                        <h6 class="card-title text-center mb-3">
                            <i class="bi bi-star"></i> 主要功能
                        </h6>
                        <div class="row text-center">
                            <div class="col-4">
                                <i class="bi bi-diagram-3 text-primary" style="font-size: 1.5rem;"></i>
                                <div class="small mt-1">TCP/UDP转发</div>
                            </div>
                            <div class="col-4">
                                <i class="bi bi-graph-up text-success" style="font-size: 1.5rem;"></i>
                                <div class="small mt-1">实时监控</div>
                            </div>
                            <div class="col-4">
                                <i class="bi bi-shield-check text-warning" style="font-size: 1.5rem;"></i>
                                <div class="small mt-1">安全管理</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            // 显示加载状态
            const submitBtn = document.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 登录中...';
            submitBtn.disabled = true;
            
            // 创建表单数据
            const formData = new FormData();
            formData.append('username', username);
            formData.append('password', password);
            
            // 发送登录请求
            fetch('/api/auth/login', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 登录成功，跳转到主页
                    window.location.href = '/';
                } else {
                    // 登录失败，显示错误信息
                    showError(data.message || '登录失败');
                }
            })
            .catch(error => {
                console.error('登录请求失败:', error);
                showError('网络错误，请稍后重试');
            })
            .finally(() => {
                // 恢复按钮状态
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });
        
        function showError(message) {
            // 移除现有的错误提示
            const existingAlert = document.querySelector('.alert');
            if (existingAlert) {
                existingAlert.remove();
            }
            
            // 创建错误提示
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-danger alert-dismissible fade show mt-3';
            alertDiv.innerHTML = `
                <i class="bi bi-exclamation-triangle"></i> ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            // 插入到表单后面
            const form = document.getElementById('loginForm');
            form.parentNode.insertBefore(alertDiv, form.nextSibling);
        }
        
        // 检查是否已经登录
        fetch('/api/metrics/current')
            .then(response => {
                if (response.ok) {
                    // 已经登录，跳转到主页
                    window.location.href = '/';
                }
            })
            .catch(() => {
                // 未登录，继续显示登录页面
            });
    </script>
</body>
</html>
