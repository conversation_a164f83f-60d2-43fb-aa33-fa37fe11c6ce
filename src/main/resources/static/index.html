<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Socket Relay - TCP/UDP转发与管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
        }
        .sidebar .nav-link {
            color: #adb5bd;
        }
        .sidebar .nav-link:hover {
            color: #fff;
        }
        .sidebar .nav-link.active {
            color: #fff;
            background-color: #495057;
        }
        .main-content {
            margin-left: 0;
        }
        .card-metric {
            border-left: 4px solid #007bff;
        }
        .card-metric.success {
            border-left-color: #28a745;
        }
        .card-metric.warning {
            border-left-color: #ffc107;
        }
        .card-metric.danger {
            border-left-color: #dc3545;
        }
        .status-badge {
            font-size: 0.75rem;
        }
        .alert-item {
            border-left: 4px solid;
            margin-bottom: 0.5rem;
        }
        .alert-item.info {
            border-left-color: #17a2b8;
        }
        .alert-item.warning {
            border-left-color: #ffc107;
        }
        .alert-item.critical {
            border-left-color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-white">Socket Relay</h5>
                        <small class="text-muted">v1.0</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#" onclick="showPage('dashboard')">
                                <i class="bi bi-speedometer2"></i> 仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('rules')">
                                <i class="bi bi-list-ul"></i> 转发规则
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('connections')">
                                <i class="bi bi-diagram-3"></i> 连接管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('metrics')">
                                <i class="bi bi-graph-up"></i> 监控指标
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('alerts')">
                                <i class="bi bi-exclamation-triangle"></i> 告警中心
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('ip-access')">
                                <i class="bi bi-shield-check"></i> IP访问控制
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('logs')">
                                <i class="bi bi-journal-text"></i> 审计日志
                            </a>
                        </li>
                    </ul>
                    
                    <hr class="text-muted">
                    
                    <div class="text-center">
                        <button class="btn btn-outline-light btn-sm" onclick="logout()">
                            <i class="bi bi-box-arrow-right"></i> 退出
                        </button>
                    </div>
                </div>
            </nav>
            
            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- 头部 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2" id="page-title">仪表板</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshData()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                        </div>
                        <div class="text-muted small">
                            最后更新: <span id="last-update">--</span>
                        </div>
                    </div>
                </div>
                
                <!-- 页面内容 -->
                <div id="page-content">
                    <!-- 仪表板页面 -->
                    <div id="dashboard-page" class="page-content">
                        <!-- 指标卡片 -->
                        <div class="row mb-4">
                            <div class="col-xl-3 col-md-6 mb-4">
                                <div class="card card-metric success">
                                    <div class="card-body">
                                        <div class="row no-gutters align-items-center">
                                            <div class="col mr-2">
                                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                    活跃连接
                                                </div>
                                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="active-connections">0</div>
                                            </div>
                                            <div class="col-auto">
                                                <i class="bi bi-diagram-3 text-success" style="font-size: 2rem;"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-xl-3 col-md-6 mb-4">
                                <div class="card card-metric">
                                    <div class="card-body">
                                        <div class="row no-gutters align-items-center">
                                            <div class="col mr-2">
                                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                    总连接数
                                                </div>
                                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-connections">0</div>
                                            </div>
                                            <div class="col-auto">
                                                <i class="bi bi-graph-up text-primary" style="font-size: 2rem;"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-xl-3 col-md-6 mb-4">
                                <div class="card card-metric warning">
                                    <div class="card-body">
                                        <div class="row no-gutters align-items-center">
                                            <div class="col mr-2">
                                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                                    传输速率
                                                </div>
                                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="bytes-per-second">0 B/s</div>
                                            </div>
                                            <div class="col-auto">
                                                <i class="bi bi-speedometer text-warning" style="font-size: 2rem;"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-xl-3 col-md-6 mb-4">
                                <div class="card card-metric danger">
                                    <div class="card-body">
                                        <div class="row no-gutters align-items-center">
                                            <div class="col mr-2">
                                                <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                                    错误率
                                                </div>
                                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="error-rate">0%</div>
                                            </div>
                                            <div class="col-auto">
                                                <i class="bi bi-exclamation-triangle text-danger" style="font-size: 2rem;"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 图表和告警 -->
                        <div class="row">
                            <div class="col-lg-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="m-0 font-weight-bold text-primary">实时监控</h6>
                                    </div>
                                    <div class="card-body">
                                        <canvas id="metricsChart" width="400" height="200"></canvas>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-lg-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="m-0 font-weight-bold text-primary">最新告警</h6>
                                    </div>
                                    <div class="card-body" id="recent-alerts">
                                        <div class="text-center text-muted">
                                            <i class="bi bi-check-circle"></i>
                                            <p>暂无告警</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- WebSocket测试区域 -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="m-0 font-weight-bold text-primary">WebSocket测试</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <button class="btn btn-outline-info btn-sm w-100" onclick="testWebSocketAlert()">
                                                    <i class="bi bi-bell"></i> 测试告警推送
                                                </button>
                                            </div>
                                            <div class="col-md-4">
                                                <button class="btn btn-outline-success btn-sm w-100" onclick="testWebSocketMetrics()">
                                                    <i class="bi bi-graph-up"></i> 测试指标推送
                                                </button>
                                            </div>
                                            <div class="col-md-4">
                                                <button class="btn btn-outline-warning btn-sm w-100" onclick="testWebSocketStatus()">
                                                    <i class="bi bi-info-circle"></i> 测试状态推送
                                                </button>
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-md-12">
                                                <button class="btn btn-outline-primary btn-sm w-100" onclick="showUdpUsageHelp()">
                                                    <i class="bi bi-question-circle"></i> 测试UDP使用说明
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 其他页面内容将通过JavaScript动态加载 -->
                    <div id="rules-page" class="page-content" style="display: none;">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="m-0 font-weight-bold text-primary">转发规则管理</h6>
                                    <small class="text-muted">
                                        <i class="bi bi-info-circle"></i> 点击规则行可查看客户端连接详情
                                    </small>
                                </div>
                                <button class="btn btn-primary btn-sm" onclick="showAddRuleModal()">
                                    <i class="bi bi-plus"></i> 添加规则
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered" id="rules-table">
                                        <thead>
                                            <tr>
                                                <th>规则名称</th>
                                                <th>协议</th>
                                                <th>监听端口</th>
                                                <th>转发配置</th>
                                                <th>服务状态</th>
                                                <th>连接数</th>
                                                <th>规则状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="rules-tbody">
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="connections-page" class="page-content" style="display: none;">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="m-0 font-weight-bold text-primary">活跃连接</h6>
                                    <small class="text-muted">
                                        <span id="history-stats-info">加载中...</span>
                                    </small>
                                </div>
                                <div>
                                    <button class="btn btn-outline-warning btn-sm me-2" onclick="showClearHistoryModal()">
                                        <i class="bi bi-trash"></i> 清除历史记录
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" onclick="loadConnectionsData()">
                                        <i class="bi bi-arrow-clockwise"></i> 刷新
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered" id="connections-table">
                                        <thead>
                                            <tr>
                                                <th>连接ID</th>
                                                <th>协议</th>
                                                <th>远程地址</th>
                                                <th>本地端口</th>
                                                <th>状态</th>
                                                <th>持续时间</th>
                                                <th>流量</th>
                                            </tr>
                                        </thead>
                                        <tbody id="connections-tbody">
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="metrics-page" class="page-content" style="display: none;">
                        <div class="row">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="m-0 font-weight-bold text-primary">性能监控</h6>
                                    </div>
                                    <div class="card-body">
                                        <canvas id="performanceChart" width="400" height="200"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="alerts-page" class="page-content" style="display: none;">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="m-0 font-weight-bold text-primary">告警历史</h6>
                            </div>
                            <div class="card-body" id="alerts-list">
                            </div>
                        </div>
                    </div>
                    
                    <div id="logs-page" class="page-content" style="display: none;">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="m-0 font-weight-bold text-primary">审计日志</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered" id="logs-table">
                                        <thead>
                                            <tr>
                                                <th>时间</th>
                                                <th>用户</th>
                                                <th>操作</th>
                                                <th>描述</th>
                                                <th>结果</th>
                                            </tr>
                                        </thead>
                                        <tbody id="logs-tbody">
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- IP访问控制页面 -->
                <div id="ip-access-page" class="page-content" style="display: none;">
                    <div class="row mb-4">
                        <!-- 统计卡片 -->
                        <div class="col-xl-4 col-md-6 mb-4">
                            <div class="card card-metric success">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">白名单规则</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="allow-rules-count">0</div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="bi bi-shield-check fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-4 col-md-6 mb-4">
                            <div class="card card-metric danger">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">黑名单规则</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="deny-rules-count">0</div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="bi bi-shield-x fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-4 col-md-6 mb-4">
                            <div class="card card-metric info">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">总规则数</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-rules-count">0</div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="bi bi-list-ul fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="m-0 font-weight-bold text-primary">IP访问控制规则</h6>
                                        <small class="text-muted">
                                            <i class="bi bi-info-circle"></i> 管理客户端TCP连接的白名单和黑名单
                                        </small>
                                    </div>
                                    <div>
                                        <button class="btn btn-success btn-sm me-2" onclick="showAddIpRuleModal()">
                                            <i class="bi bi-plus"></i> 添加规则
                                        </button>
                                        <button class="btn btn-info btn-sm" onclick="showIpTestModal()">
                                            <i class="bi bi-search"></i> 测试IP
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-hover" id="ip-rules-table">
                                            <thead>
                                                <tr>
                                                    <th>IP地址/网段</th>
                                                    <th>访问类型</th>
                                                    <th>适用规则</th>
                                                    <th>优先级</th>
                                                    <th>描述</th>
                                                    <th>状态</th>
                                                    <th>创建时间</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody id="ip-rules-tbody">
                                                <!-- 动态加载 -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 添加规则模态框 -->
    <div class="modal fade" id="addRuleModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加转发规则</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addRuleForm">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="ruleName" class="form-label">规则名称</label>
                                    <input type="text" class="form-control" id="ruleName" required>
                                </div>
                                <div class="mb-3">
                                    <label for="protocol" class="form-label">协议类型</label>
                                    <select class="form-select" id="protocol" required>
                                        <option value="TCP">TCP - 点对点转发</option>
                                        <option value="UDP">UDP - 一对多广播</option>
                                        <option value="TCP_UDP">TCP+UDP - 混合模式</option>
                                    </select>
                                    <div class="form-text">
                                        <div id="protocolHelp">
                                            <strong>TCP</strong>: 传统的点对点转发模式<br>
                                            <strong>UDP</strong>: 一对多广播模式，支持客户端订阅机制<br>
                                            <strong>TCP+UDP</strong>: 同时支持TCP和UDP协议
                                        </div>
                                        <button type="button" class="btn btn-link btn-sm p-0 mt-1" onclick="showUdpUsageHelp()">
                                            <i class="bi bi-question-circle"></i> UDP广播模式使用说明
                                        </button>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="sourcePort" class="form-label" id="sourcePortLabel">监听端口</label>
                                            <input type="number" class="form-control" id="sourcePort" min="1" max="65535" required>
                                            <div class="form-text" id="sourcePortHelp">本服务监听的端口号</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="targetPort" class="form-label" id="targetPortLabel">转发目标端口</label>
                                            <input type="number" class="form-control" id="targetPort" min="1" max="65535" required>
                                            <div class="form-text" id="targetPortHelp">数据转发到的目标端口</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="targetIp" class="form-label" id="targetIpLabel">转发目标IP</label>
                                    <input type="text" class="form-control" id="targetIp" required>
                                    <div class="form-text" id="targetIpHelp">数据转发到的目标服务器IP地址</div>
                                </div>
                                <div class="mb-3">
                                    <label for="dataSourceName" class="form-label">数据源连接地址名称</label>
                                    <input type="text" class="form-control" id="dataSourceName" placeholder="可选，用于显示友好的名称">
                                </div>
                                <div class="mb-3">
                                    <label for="remark" class="form-label">备注</label>
                                    <textarea class="form-control" id="remark" rows="2"></textarea>
                                </div>
                            </div>

                            <!-- 右侧高级配置 -->
                            <div class="col-md-4">
                                <h6 class="text-primary">
                                    <i class="bi bi-gear"></i> 高级配置
                                </h6>
                                <div class="card">
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="autoReconnect" class="form-label">自动重连</label>
                                            <select class="form-select form-select-sm" id="autoReconnect">
                                                <option value="true" selected>启用</option>
                                                <option value="false">禁用</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="reconnectInterval" class="form-label">重连间隔(秒)</label>
                                            <input type="number" class="form-control form-control-sm" id="reconnectInterval" value="5" min="1" max="300">
                                        </div>
                                        <div class="mb-3">
                                            <label for="maxReconnectAttempts" class="form-label">最大重连次数</label>
                                            <input type="number" class="form-control form-control-sm" id="maxReconnectAttempts" value="10" min="1" max="100">
                                        </div>
                                        <div class="mb-3">
                                            <label for="connectionPoolSize" class="form-label">连接池大小</label>
                                            <input type="number" class="form-control form-control-sm" id="connectionPoolSize" value="1" min="1" max="10">
                                        </div>
                                        <small class="text-muted">
                                            <i class="bi bi-info-circle"></i>
                                            这些参数控制数据源连接的重连行为和连接池配置
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="addRule()">添加</button>
                </div>
            </div>
        </div>
    </div>

    <!-- UDP使用说明模态框 -->
    <div class="modal fade" id="udpUsageModal" tabindex="-1" aria-labelledby="udpUsageModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="udpUsageModalLabel">
                        <i class="bi bi-broadcast"></i> UDP广播转发使用说明
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <h6><i class="bi bi-diagram-3"></i> 双端口架构说明</h6>
                        <p><strong>下游监听端口</strong>：供客户端连接、订阅和接收广播数据<br>
                        <strong>上游接收端口</strong>：接收需要广播的原始数据</p>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-success">下游客户端操作</h6>
                            <div class="bg-light p-2 rounded mb-2">
                                <small><strong>订阅：</strong></small><br>
                                <code>echo 'SUBSCRIBE' | nc -u IP 下游端口</code>
                            </div>
                            <div class="bg-light p-2 rounded mb-2">
                                <small><strong>心跳：</strong></small><br>
                                <code>echo 'HEARTBEAT' | nc -u IP 下游端口</code>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-warning">上游数据发送</h6>
                            <div class="bg-light p-2 rounded mb-2">
                                <small><strong>发送数据：</strong></small><br>
                                <code>echo '数据' | nc -u IP 上游端口</code>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1.5.2/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@stomp/stompjs@6.1.2/bundles/stomp.umd.min.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
