# 🐛 Socket Relay系统 BUG跟踪记录表

## 📋 BUG统计概览

| 状态 | 数量 | 占比 |
|------|------|------|
| 🔴 严重 | 3 | 30% |
| 🟡 重要 | 3 | 30% |
| 🟢 一般 | 3 | 30% |
| ✅ 已修复 | 2 | 20% |
| **总计** | **10** | **100%** |

---

## 🔴 严重BUG (优先级：高)

### BUG-001: 菜单导航功能失效
- **发现日期**: 2025-08-04
- **报告人**: 系统分析
- **严重程度**: 🔴 严重
- **状态**: 🔄 进行中
- **影响范围**: 前端用户界面
- **问题描述**: 点击导航菜单无法正确跳转到对应页面，始终停留在仪表板页面
- **技术细节**: 
  - 文件位置: `src/main/resources/static/js/app.js`
  - 问题函数: `showPage()`, `bindNavigationEvents()`
  - 根本原因: 事件绑定冲突，DOM操作失败
- **复现步骤**: 
  1. 登录系统
  2. 点击任意菜单项（转发规则、连接管理等）
  3. 页面不跳转，仍显示仪表板
- **预期修复时间**: 2025-08-05
- **修复负责人**: 待分配

### BUG-002: WebSocket连接不稳定
- **发现日期**: 2025-08-04
- **报告人**: 系统分析
- **严重程度**: 🔴 严重
- **状态**: 🔄 进行中
- **影响范围**: 实时通信功能
- **问题描述**: WebSocket连接频繁失败，导致实时告警和监控数据无法推送
- **技术细节**:
  - 文件位置: `src/main/resources/static/js/app.js`
  - 问题函数: `connectWebSocket()`, `tryLegacyWebSocket()`
  - 根本原因: 库检测逻辑错误，SockJS/Stomp库加载检测失败
- **错误日志**: 
  ```
  WebSocket连接失败，将使用后备方案
  WebSocket库未完全加载，跳过连接
  STOMP: Connection closed to ws://127.0.0.1:8080/ws
  ```
- **预期修复时间**: 2025-08-05
- **修复负责人**: 待分配

### BUG-003: 页面刷新后状态丢失
- **发现日期**: 2025-08-04
- **报告人**: 用户反馈
- **严重程度**: 🔴 严重
- **状态**: 🔄 进行中
- **影响范围**: 用户体验
- **问题描述**: 页面刷新后总是跳转到仪表板，无法保持当前页面状态
- **技术细节**:
  - 文件位置: `src/main/resources/static/js/app.js`
  - 问题函数: `getCurrentPageFromUrl()`, `initializeApp()`
  - 根本原因: URL路由解析和页面初始化逻辑问题
- **预期修复时间**: 2025-08-05
- **修复负责人**: 待分配

---

## 🟡 重要BUG (优先级：中)

### BUG-004: 告警页面内容为空
- **发现日期**: 2025-08-04
- **报告人**: 系统分析
- **严重程度**: 🟡 重要
- **状态**: 📋 待处理
- **影响范围**: 告警管理功能
- **问题描述**: 告警页面虽然可以访问，但内容区域为空，无法查看告警历史
- **技术细节**:
  - 文件位置: `src/main/resources/static/index.html` (line 364-372)
  - 缺失功能: 告警列表显示组件
  - 后端API: 已实现，前端展示缺失
- **预期修复时间**: 2025-08-06
- **修复负责人**: 待分配

### BUG-005: 审计日志查询功能未实现
- **发现日期**: 2025-08-04
- **报告人**: 系统分析
- **严重程度**: 🟡 重要
- **状态**: 📋 待处理
- **影响范围**: 审计功能
- **问题描述**: 审计日志页面存在但查询功能未实现，无法查看操作历史
- **技术细节**:
  - 文件位置: `src/main/resources/static/js/app.js`
  - 问题函数: `loadLogsData()` - 仅有TODO注释
  - 后端API: 已实现，前端调用缺失
- **预期修复时间**: 2025-08-07
- **修复负责人**: 待分配

### BUG-006: 性能监控图表缺失
- **发现日期**: 2025-08-04
- **报告人**: 系统分析
- **严重程度**: 🟡 重要
- **状态**: 📋 待处理
- **影响范围**: 监控功能
- **问题描述**: 监控页面图表容器为空，无法可视化展示性能数据
- **技术细节**:
  - 文件位置: `src/main/resources/static/index.html` (line 349-362)
  - 缺失组件: Chart.js图表实现
  - 数据源: 后端指标API已就绪
- **预期修复时间**: 2025-08-08
- **修复负责人**: 待分配

### BUG-007: UDP会话管理内存泄漏风险
- **发现日期**: 2025-08-04
- **报告人**: 代码审查
- **严重程度**: 🟡 重要
- **状态**: ✅ 已修复
- **影响范围**: 系统稳定性
- **问题描述**: UDP会话映射可能不会及时清理，长时间运行存在内存泄漏风险
- **修复详情**:
  - 修复日期: 2025-08-04
  - 修复内容: 实现完整的UDP会话管理器，包含自动清理机制
  - 新增组件: `UdpSessionManager`, `UdpSessionController`
  - 技术改进: 会话超时检测、定期清理、内存监控
- **修复验证**:
  - ✅ 编译通过
  - ✅ 会话自动清理机制
  - ✅ 内存泄漏风险消除
  - ✅ 监控API可用

---

## 🟢 一般BUG (优先级：低)

### BUG-008: Toast提示位置不符合预期
- **发现日期**: 2025-08-04
- **报告人**: 用户反馈
- **严重程度**: 🟢 一般
- **状态**: ✅ 已修复
- **影响范围**: 用户界面
- **问题描述**: Toast提示消息显示在右上角，用户期望在右下角
- **修复详情**:
  - 修复日期: 2025-08-04
  - 修复内容: 将CSS类从`top-0 end-0`改为`bottom-0 end-0`
  - 文件位置: `src/main/resources/static/js/app.js`

### BUG-009: H2数据库配置优化需求
- **发现日期**: 2025-08-04
- **报告人**: 系统分析
- **严重程度**: 🟢 一般
- **状态**: 📋 待处理
- **影响范围**: 数据库性能
- **问题描述**: H2数据库使用MODE=LEGACY可能不是最佳配置选择
- **技术细节**:
  - 文件位置: `src/main/resources/application.yml`
  - 当前配置: `MODE=LEGACY`
  - 建议优化: 评估是否需要LEGACY模式
- **预期修复时间**: 2025-08-10
- **修复负责人**: 待分配

### BUG-010: 应用层心跳功能不完整
- **发现日期**: 2025-08-04
- **报告人**: 需求对比
- **严重程度**: 🟢 一般
- **状态**: 📋 待处理
- **影响范围**: 连接保活
- **问题描述**: 需求要求每60秒发送心跳包，当前只有空闲检测，缺少主动心跳发送
- **技术细节**:
  - 文件位置: `src/main/java/com/ux/relay/core/TcpForwardingHandler.java`
  - 当前实现: 仅有IdleStateHandler空闲检测
  - 缺失功能: 主动心跳包发送和响应检测
- **预期修复时间**: 2025-08-11
- **修复负责人**: 待分配

---

## 📊 BUG修复进度跟踪

### 本周修复计划 (2025-08-04 ~ 2025-08-10)
- [x] BUG-008: Toast提示位置 ✅ 已完成
- [ ] BUG-001: 菜单导航功能 🔄 进行中
- [ ] BUG-002: WebSocket连接 🔄 进行中
- [ ] BUG-003: 页面状态保持 🔄 进行中
- [ ] BUG-004: 告警页面内容 📋 计划中

### 下周修复计划 (2025-08-11 ~ 2025-08-17)
- [ ] BUG-005: 审计日志查询
- [ ] BUG-006: 性能监控图表
- [ ] BUG-007: UDP会话管理
- [ ] BUG-009: H2数据库配置
- [ ] BUG-010: 应用层心跳

---

## 📝 修复记录模板

```markdown
### BUG-XXX: [BUG标题]
- **发现日期**: YYYY-MM-DD
- **报告人**: [报告人姓名]
- **严重程度**: 🔴/🟡/🟢 [严重/重要/一般]
- **状态**: 📋待处理/🔄进行中/✅已修复/❌已关闭
- **影响范围**: [功能模块]
- **问题描述**: [详细描述]
- **技术细节**: [文件位置、问题代码等]
- **复现步骤**: [如何复现问题]
- **预期修复时间**: YYYY-MM-DD
- **修复负责人**: [负责人姓名]
- **修复详情**: [修复完成后填写]
```

---

## 🔄 更新日志

- **2025-08-04**: 创建BUG跟踪表，记录10个已发现的BUG
- **2025-08-04**: 修复BUG-008 Toast提示位置问题
- **2025-08-04**: 修复BUG-007 UDP会话管理内存泄漏风险

---

*最后更新: 2025-08-04*
*维护人员: Socket Relay开发团队*
